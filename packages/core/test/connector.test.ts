import { describe, it, expect, vi, beforeEach, afterEach, beforeAll, afterAll } from 'vitest'
import { ws, type WebSocketHandler} from 'msw'
import { setupServer } from 'msw/node'
import ExpiryMap from 'expiry-map'
import EventEmitter from 'eventemitter3'
import { WebshellConnector } from '../src/connector'
import {
  jsonrpcRequest,
  jsonrpcNotification,
  jsonrpcSuccessRespone,
  jsonrpcErrorResponse,
  type JsonrpcRequest,
  type JsonrpcNotification,
} from '../src/utils/json-rpc'

// 创建 WebSocket link
const testUrl = 'ws://localhost:8080/test'
const wsLink = ws.link(testUrl)

let client: 

// 设置 WebSocket connection handler
wsLink.addEventListener('connection', ({ client }) => {
  wsManager.addConnection(testUrl, { client })
})

// 创建 MSW 服务器
const server = setupServer()

describe('WebshellConnector', { timeout: 1000 }, () => {
  let connector: WebshellConnector

  beforeAll(() => {
    // 启动 MSW 服务器
    server.listen({ onUnhandledRequest: 'error' })

    // 设置 WebSocket handler
    server.use(wsLink as any)
  })

  afterAll(() => {
    // 关闭 MSW 服务器
    server.close()
  })

  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks()
    wsManager.clear()
  })

  afterEach(() => {
    if (connector) {
      connector.close()
    }
    server.resetHandlers()
    wsManager.clear()
  })

  describe('构造函数', () => {
    it('应该正确初始化 WebshellConnector', () => {
      connector = new WebshellConnector(testUrl)
      expect(connector).toBeInstanceOf(WebshellConnector)
      expect(connector.url).toBe(testUrl)
    })

    it('应该使用默认选项', () => {
      connector = new WebshellConnector(testUrl)
      expect(connector).toBeInstanceOf(WebshellConnector)
      // 验证内部属性通过行为测试
    })

    it('应该接受自定义选项', () => {
      const customPromiseStore = new ExpiryMap(5000)
      const customEventEmitter = new EventEmitter()
      const customTimeout = 5000

      connector = new WebshellConnector(testUrl, undefined, {
        promiseStore: customPromiseStore,
        eventEmitter: customEventEmitter,
        requestTimeout: customTimeout,
      })

      expect(connector).toBeInstanceOf(WebshellConnector)
    })

    it('应该支持协议参数', () => {
      const protocols = ['protocol1', 'protocol2']
      connector = new WebshellConnector(testUrl, protocols)
      expect(connector).toBeInstanceOf(WebshellConnector)
    })

    it('应该支持 URL 对象', () => {
      const urlObj = new URL(testUrl)
      connector = new WebshellConnector(urlObj)
      expect(connector.url).toBe(testUrl)
    })
  })

  describe('sendRequest', () => {
    beforeEach(async () => {
      connector = new WebshellConnector(testUrl)

      // 等待 WebSocket 连接建立
      await new Promise<void>(resolve => {
        connector.addEventListener('open', () => resolve())
      })
    })

    it('应该发送 JSON-RPC 请求并返回 Promise', async () => {
      const request: JsonrpcRequest<{ test: string }> = jsonrpcRequest({
        id: 'test-request-1',
        method: 'test.method',
        params: { test: 'value' },
      })

      // 设置响应处理
      const connection = wsManager.getConnection(testUrl)
      if (connection) {
        connection.client.addEventListener('message', (event: MessageEvent) => {
          const receivedRequest = JSON.parse(event.data)
          if (receivedRequest.id === 'test-request-1') {
            // 模拟服务器响应
            const response = jsonrpcSuccessRespone({
              id: 'test-request-1',
              result: { success: true },
            })
            connection.client.send(JSON.stringify(response))
          }
        })
      }

      const responsePromise = connector.sendRequest(request)
      const result = await responsePromise

      expect('result' in result && result.result).toEqual({ success: true })
    })

    it('应该处理错误响应', async () => {
      const request: JsonrpcRequest = jsonrpcRequest({
        id: 'test-request-error',
        method: 'test.error',
      })

      // 设置错误响应处理
      const connection = wsManager.getConnection(testUrl)
      if (connection) {
        connection.client.addEventListener('message', (event: MessageEvent) => {
          const receivedRequest = JSON.parse(event.data)
          if (receivedRequest.id === 'test-request-error') {
            // 模拟服务器错误响应
            const errorResponse = jsonrpcErrorResponse({
              id: 'test-request-error',
              error: {
                code: -32601,
                message: 'Method not found',
              },
            })
            connection.client.send(JSON.stringify(errorResponse))
          }
        })
      }

      const responsePromise = connector.sendRequest(request)
      await expect(responsePromise).rejects.toThrow('Method not found')
    })

    it('应该在请求超时时拒绝 Promise', async () => {
      const shortTimeoutConnector = new WebshellConnector(testUrl, undefined, {
        requestTimeout: 100,
      })

      // 等待连接建立
      await new Promise<void>(resolve => {
        shortTimeoutConnector.addEventListener('open', () => resolve())
      })

      const request: JsonrpcRequest = jsonrpcRequest({
        id: 'timeout-request',
        method: 'test.timeout',
      })

      const responsePromise = shortTimeoutConnector.sendRequest(request)
      await expect(responsePromise).rejects.toThrow('Request timeout: timeout-request')

      shortTimeoutConnector.close()
    }, 200)

    it('应该支持负数超时（禁用超时）', async () => {
      const noTimeoutConnector = new WebshellConnector(testUrl, undefined, {
        requestTimeout: -1,
      })

      // 等待连接建立
      await new Promise<void>(resolve => {
        noTimeoutConnector.addEventListener('open', () => resolve())
      })

      const request: JsonrpcRequest = jsonrpcRequest({
        id: 'no-timeout-request',
        method: 'test.method',
      })

      const responsePromise = noTimeoutConnector.sendRequest(request)

      // 等待一小段时间确保没有超时
      await new Promise(resolve => setTimeout(resolve, 50))

      // 模拟响应
      const connection = wsManager.getConnection(testUrl)
      if (connection) {
        const response = jsonrpcSuccessRespone({
          id: 'no-timeout-request',
          result: 'success',
        })
        connection.client.send(JSON.stringify(response))
      }

      const result = await responsePromise
      expect('result' in result && result.result).toBe('success')

      noTimeoutConnector.close()
    })

    it('应该处理多个并发请求', async () => {
      const requests = [
        jsonrpcRequest({ id: 'req-1', method: 'method1' }),
        jsonrpcRequest({ id: 'req-2', method: 'method2' }),
        jsonrpcRequest({ id: 'req-3', method: 'method3' }),
      ]

      // 设置响应处理
      const connection = wsManager.getConnection(testUrl)
      if (connection) {
        connection.client.addEventListener('message', (event: MessageEvent) => {
          const receivedRequest = JSON.parse(event.data)

          // 以不同顺序发送响应
          const responses = [
            { id: 'req-2', result: 'result2' },
            { id: 'req-1', result: 'result1' },
            { id: 'req-3', result: 'result3' },
          ]

          const response = responses.find(r => r.id === receivedRequest.id)
          if (response) {
            const jsonResponse = jsonrpcSuccessRespone(response)
            connection.client.send(JSON.stringify(jsonResponse))
          }
        })
      }

      const promises = requests.map(req => connector.sendRequest(req))
      const results = await Promise.all(promises)

      expect('result' in results[0] && results[0].result).toBe('result1')
      expect('result' in results[1] && results[1].result).toBe('result2')
      expect('result' in results[2] && results[2].result).toBe('result3')
    })
  })

  describe('sendNotification', () => {
    beforeEach(async () => {
      connector = new WebshellConnector(testUrl)

      // 等待 WebSocket 连接建立
      await new Promise<void>(resolve => {
        connector.addEventListener('open', () => resolve())
      })
    })

    it('应该发送 JSON-RPC 通知', () => {
      const notification: JsonrpcNotification<{ message: string }> = jsonrpcNotification({
        method: 'notification.test',
        params: { message: 'hello' },
      })

      const sendSpy = vi.spyOn(connector, 'send')
      connector.sendNotification(notification)

      expect(sendSpy).toHaveBeenCalledWith(JSON.stringify(notification))
    })

    it('应该发送没有参数的通知', () => {
      const notification: JsonrpcNotification = jsonrpcNotification({
        method: 'simple.notification',
      })

      const sendSpy = vi.spyOn(connector, 'send')
      connector.sendNotification(notification)

      expect(sendSpy).toHaveBeenCalledWith(JSON.stringify(notification))
    })
  })

  describe('事件处理', () => {
    beforeEach(async () => {
      connector = new WebshellConnector(testUrl)

      // 等待 WebSocket 连接建立
      await new Promise<void>(resolve => {
        connector.addEventListener('open', () => resolve())
      })
    })

    it('应该处理 JSON-RPC 通知事件', () => {
      const customEventEmitter = new EventEmitter()
      const eventSpy = vi.fn()
      customEventEmitter.on('test.notification', eventSpy)

      const customConnector = new WebshellConnector(testUrl, undefined, {
        eventEmitter: customEventEmitter,
      })

      // 模拟接收通知
      const notification = jsonrpcNotification({
        method: 'test.notification',
        params: { data: 'test' },
      })

      const messageEvent = new MessageEvent('message', {
        data: JSON.stringify(notification),
      })

      customConnector.dispatchEvent(messageEvent)

      expect(eventSpy).toHaveBeenCalledWith({ data: 'test' })

      customConnector.close()
    })

    it('应该在连接关闭时拒绝所有待处理的请求', async () => {
      const request1 = jsonrpcRequest({ id: 'req-1', method: 'method1' })
      const request2 = jsonrpcRequest({ id: 'req-2', method: 'method2' })

      const promise1 = connector.sendRequest(request1)
      const promise2 = connector.sendRequest(request2)

      // 模拟连接关闭
      const closeEvent = new Event('close')
      connector.dispatchEvent(closeEvent)

      await expect(promise1).rejects.toThrow('Connection closed')
      await expect(promise2).rejects.toThrow('Connection closed')
    })
  })

  describe('错误处理', () => {
    beforeEach(async () => {
      connector = new WebshellConnector(testUrl)

      // 等待 WebSocket 连接建立
      await new Promise<void>(resolve => {
        connector.addEventListener('open', () => resolve())
      })
    })

    it('应该处理无效的 JSON-RPC 消息', () => {
      const invalidMessage = 'invalid json'
      const messageEvent = new MessageEvent('message', {
        data: invalidMessage,
      })

      // 这应该不会抛出错误，而是被忽略
      expect(() => {
        connector.dispatchEvent(messageEvent)
      }).not.toThrow()
    })

    it('应该处理未知 ID 的响应', () => {
      const response = jsonrpcSuccessRespone({
        id: 'unknown-id',
        result: 'test',
      })

      const messageEvent = new MessageEvent('message', {
        data: JSON.stringify(response),
      })

      // 这应该不会抛出错误，而是被忽略
      expect(() => {
        connector.dispatchEvent(messageEvent)
      }).not.toThrow()
    })
  })
})
